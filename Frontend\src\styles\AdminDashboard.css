/* AdminDashboard Component Styles */
.AdminDashboard {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Metrics Cards */
.AdminDashboard .AdminDashboard__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminDashboard .metric-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: var(--heading6);
  box-shadow: var(--box-shadow-light);
  border-left: 4px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.AdminDashboard .metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.AdminDashboard .metric-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminDashboard .metric-card.clickable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.AdminDashboard .metric-card.clickable:active {
  transform: translateY(-2px);
}

.AdminDashboard .metric-card.buyers {
  border-left-color: #3b82f6;
}

.AdminDashboard .metric-card.sellers {
  border-left-color: #10b981;
}

.AdminDashboard .metric-card.content {
  border-left-color: #f59e0b;
}

.AdminDashboard .metric-card.revenue {
  border-left-color: var(--btn-color);
}

.AdminDashboard .metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: var(--heading4);
  color: var(--white);
}

.AdminDashboard .metric-card.buyers .metric-icon {
  background-color: #3b82f6;
}

.AdminDashboard .metric-card.sellers .metric-icon {
  background-color: #10b981;
}

.AdminDashboard .metric-card.content .metric-icon {
  background-color: #f59e0b;
}

.AdminDashboard .metric-card.revenue .metric-icon {
  background-color: var(--btn-color);
}

.AdminDashboard .metric-content {
  flex: 1;
}

.AdminDashboard .metric-number {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 4px;
}

.AdminDashboard .metric-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Section Styles */
.AdminDashboard .AdminDashboard__section {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
}

.AdminDashboard .section-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

.AdminDashboard .section-header h2 {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.AdminDashboard .section-icon {
  color: var(--btn-color);
}

.AdminDashboard .approval-count {
  background-color: var(--btn-color);
  color: var(--white);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  margin-left: var(--smallfont);
}

/* Approvals List */
.AdminDashboard .approvals-list {
  padding: var(--basefont);
  display: grid;
  gap: var(--basefont);
}

.AdminDashboard .approval-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.AdminDashboard .approval-item:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.AdminDashboard .approval-content.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminDashboard .approval-content h4 {
  margin: 0 0 4px 0;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.AdminDashboard .approval-content p {
  margin: 0 0 8px 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminDashboard .approval-category {
  display: inline-block;
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: var(--smallfont);
}

.AdminDashboard .approval-date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.AdminDashboard .approval-actions {
  display: flex;
  gap: var(--smallfont);
}

.AdminDashboard .btn-approve,
.AdminDashboard .btn-reject,
.AdminDashboard .btn-view {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminDashboard .btn-approve {
  background-color: #10b981;
  color: var(--white);
}

.AdminDashboard .btn-approve:hover {
  background-color: #059669;
}

.AdminDashboard .btn-reject {
  background-color: #ef4444;
  color: var(--white);
}

.AdminDashboard .btn-reject:hover {
  background-color: #dc2626;
}

.AdminDashboard .btn-view {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.AdminDashboard .btn-view:hover {
  background-color: var(--light-gray);
}

/* Tables Section */
.AdminDashboard .AdminDashboard__tables {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
}

.AdminDashboard .AdminDashboard__table-section {
  background-color: var(--white);
  border-radius: var(--border-radius);

  overflow: hidden;
}

.AdminDashboard .table-header {
  padding: var(--basefont) var(--heading6);
  background-color: #F5F5F5;
}

.AdminDashboard .table-header h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.AdminDashboard .table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.AdminDashboard .admin-table {
  width: 100%;
  border-collapse: collapse;
}

.AdminDashboard .admin-table th,
.AdminDashboard .admin-table td {
  padding: var(--smallfont) var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
  white-space: nowrap;
}

.AdminDashboard .admin-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.AdminDashboard .admin-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.AdminDashboard .admin-table tr:hover {
  background-color: var(--bg-gray);
}

.AdminDashboard .user-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminDashboard .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--smallfont);
  overflow: hidden;
  flex-shrink: 0;
}

.AdminDashboard .user-avatar img {
  width: 32px;
  height: 32px;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
}

.AdminDashboard .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.AdminDashboard .status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.AdminDashboard .status-badge.inactive {
  background-color: #fef2f2;
  color: #991b1b;
}

.AdminDashboard .status-badge.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.AdminDashboard .table-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: center;
}

/* Ensure action buttons in dashboard tables use consistent icon styling */
.AdminDashboard .table-actions .btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  background-color: transparent !important;
  color: var(--black);
}
.AdminDashboard .table-actions .btn-action.view {
  color: var(--black);
}

.AdminDashboard .table-actions .btn-action.view:hover {
  color: var(--btn-color);
  transform: scale(1.05);
}

.AdminDashboard .table-actions .btn-action.edit:hover {
  transform: scale(1.05);
  color: var(--second-primary-color);
}

.AdminDashboard .btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.AdminDashboard .btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.AdminDashboard .btn-action.edit:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.AdminDashboard .btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.AdminDashboard .btn-action.delete:hover {
  background-color: #fee2e2;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.AdminDashboard .btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.AdminDashboard .btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminDashboard .AdminDashboard__tables {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .AdminDashboard .AdminDashboard__metrics {
    grid-template-columns: 1fr 1fr;
    gap: var(--smallfont);
  }

  .AdminDashboard .metric-card {
    padding: var(--basefont);
  }

  .AdminDashboard .metric-icon {
    width: 48px;
    height: 48px;
    font-size: var(--heading5);
  }

  .AdminDashboard .metric-number {
    font-size: var(--heading4);
  }

  .AdminDashboard .approval-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .AdminDashboard .approval-actions {
    width: 100%;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: 4px;
  }

  .AdminDashboard .admin-table {
    font-size: var(--extrasmallfont);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .AdminDashboard .admin-table th,
  .AdminDashboard .admin-table td {
    padding: 8px 4px;
  }

  .AdminDashboard .table-actions {
    gap: 4px;
  }

  .AdminDashboard .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }
}

@media (max-width: 480px) {
  .AdminDashboard .AdminDashboard__metrics {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .AdminDashboard .metric-card {
    padding: var(--smallfont);
  }

  .AdminDashboard .metric-icon {
    width: 40px;
    height: 40px;
    font-size: var(--basefont);
  }

  .AdminDashboard .metric-number {
    font-size: var(--heading5);
  }

  .AdminDashboard .approval-actions {
    flex-wrap: wrap;
    gap: 4px;
  }

  .AdminDashboard .btn-approve,
  .AdminDashboard .btn-reject,
  .AdminDashboard .btn-view {
    flex: 1;
    min-width: 80px;
    justify-content: center;
    font-size: var(--extrasmallfont);
    padding: 4px 8px;
  }

  .AdminDashboard .table-actions {
    
    gap: 2px;
  }

  .AdminDashboard .btn-action {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  /* Hide tooltips on mobile */
  .AdminDashboard .btn-action::before {
    display: none;
  }
}
